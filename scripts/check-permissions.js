#!/usr/bin/env node

/**
 * 权限系统部署检查脚本
 * 验证权限控制系统的关键组件是否正确配置
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查权限控制系统配置...\n');

const checks = [];

// 检查 middleware.ts 是否存在并包含权限验证逻辑
function checkMiddleware() {
  const middlewarePath = path.join(process.cwd(), 'middleware.ts');
  
  if (!fs.existsSync(middlewarePath)) {
    return { success: false, message: 'middleware.ts 文件不存在' };
  }
  
  const content = fs.readFileSync(middlewarePath, 'utf8');
  
  if (!content.includes('isWorkspaceAdminForMiddleware')) {
    return { success: false, message: 'middleware.ts 缺少权限验证逻辑' };
  }
  
  if (!content.includes('admin')) {
    return { success: false, message: 'middleware.ts 缺少 admin 路由匹配' };
  }
  
  return { success: true, message: 'Middleware 配置正确' };
}

// 检查权限验证函数
function checkPermissionFunctions() {
  const workspacePath = path.join(process.cwd(), 'app/utils/workspace.ts');
  
  if (!fs.existsSync(workspacePath)) {
    return { success: false, message: 'app/utils/workspace.ts 文件不存在' };
  }
  
  const content = fs.readFileSync(workspacePath, 'utf8');
  
  const requiredFunctions = [
    'isWorkspaceAdmin',
    'isWorkspaceAdminForMiddleware',
    'clearUserPermissionCache'
  ];
  
  for (const func of requiredFunctions) {
    if (!content.includes(func)) {
      return { success: false, message: `缺少权限函数: ${func}` };
    }
  }
  
  return { success: true, message: '权限验证函数配置正确' };
}

// 检查缓存系统
function checkCacheSystem() {
  const cachePath = path.join(process.cwd(), 'app/utils/middleware-cache.ts');
  
  if (!fs.existsSync(cachePath)) {
    return { success: false, message: 'app/utils/middleware-cache.ts 文件不存在' };
  }
  
  const content = fs.readFileSync(cachePath, 'utf8');
  
  const requiredFunctions = [
    'getCachedAdminPermission',
    'setCachedAdminPermission',
    'clearUserAdminCache'
  ];
  
  for (const func of requiredFunctions) {
    if (!content.includes(func)) {
      return { success: false, message: `缺少缓存函数: ${func}` };
    }
  }
  
  return { success: true, message: '缓存系统配置正确' };
}

// 检查日志系统
function checkLoggerSystem() {
  const loggerPath = path.join(process.cwd(), 'app/utils/middleware-logger.ts');
  
  if (!fs.existsSync(loggerPath)) {
    return { success: false, message: 'app/utils/middleware-logger.ts 文件不存在' };
  }
  
  const content = fs.readFileSync(loggerPath, 'utf8');
  
  const requiredFunctions = [
    'logPermissionEvent',
    'getPerformanceMetrics',
    'detectAnomalousAccess'
  ];
  
  for (const func of requiredFunctions) {
    if (!content.includes(func)) {
      return { success: false, message: `缺少日志函数: ${func}` };
    }
  }
  
  return { success: true, message: '日志系统配置正确' };
}

// 检查安全监控页面
function checkSecurityPage() {
  const securityPagePath = path.join(process.cwd(), 'app/[workspace]/admin/security/page.tsx');
  
  if (!fs.existsSync(securityPagePath)) {
    return { success: false, message: '安全监控页面不存在' };
  }
  
  return { success: true, message: '安全监控页面配置正确' };
}

// 检查 API 路由
function checkSecurityAPI() {
  const apiPath = path.join(process.cwd(), 'app/api/[workspace]/admin/security/route.ts');
  
  if (!fs.existsSync(apiPath)) {
    return { success: false, message: '安全监控 API 不存在' };
  }
  
  const content = fs.readFileSync(apiPath, 'utf8');
  
  if (!content.includes('isWorkspaceAdmin')) {
    return { success: false, message: 'API 缺少权限验证' };
  }
  
  return { success: true, message: '安全监控 API 配置正确' };
}

// 检查 admin layout 更新
function checkAdminLayout() {
  const layoutPath = path.join(process.cwd(), 'app/[workspace]/admin/layout.tsx');
  
  if (!fs.existsSync(layoutPath)) {
    return { success: false, message: 'Admin layout 不存在' };
  }
  
  const content = fs.readFileSync(layoutPath, 'utf8');
  
  if (!content.includes('security')) {
    return { success: false, message: 'Admin layout 缺少安全监控菜单' };
  }
  
  return { success: true, message: 'Admin layout 配置正确' };
}

// 执行所有检查
const allChecks = [
  { name: 'Middleware 配置', check: checkMiddleware },
  { name: '权限验证函数', check: checkPermissionFunctions },
  { name: '缓存系统', check: checkCacheSystem },
  { name: '日志系统', check: checkLoggerSystem },
  { name: '安全监控页面', check: checkSecurityPage },
  { name: '安全监控 API', check: checkSecurityAPI },
  { name: 'Admin Layout', check: checkAdminLayout }
];

let allPassed = true;

allChecks.forEach(({ name, check }) => {
  const result = check();
  const status = result.success ? '✅' : '❌';
  console.log(`${status} ${name}: ${result.message}`);
  
  if (!result.success) {
    allPassed = false;
  }
});

console.log('\n' + '='.repeat(50));

if (allPassed) {
  console.log('🎉 所有检查通过！权限控制系统配置正确。');
  console.log('\n📋 下一步：');
  console.log('1. 运行测试: npm test');
  console.log('2. 启动开发服务器: npm run dev');
  console.log('3. 访问 /[workspace]/admin/security 查看监控面板');
  process.exit(0);
} else {
  console.log('⚠️  发现配置问题，请修复后重新检查。');
  process.exit(1);
}
