# Middleware 权限控制系统

## 概述

本系统实现了多层权限控制机制，确保只有具有管理员权限的用户才能访问 `/[workspace]/admin` 路由。系统采用了缓存优化、详细日志记录和实时监控功能。

## 架构设计

### 三层防护策略

1. **Middleware 路由保护（第一层）**
   - 在请求到达页面组件前进行拦截
   - 返回 404 而非 403，隐藏路由存在性
   - 使用缓存机制提高性能

2. **Layout 级权限验证（第二层）**
   - 在 admin layout 中进行权限检查
   - 提供友好的错误页面
   - 处理权限状态变化

3. **Server Actions 权限验证（第三层）**
   - 保护所有数据操作
   - 确保 API 调用的安全性

## 核心组件

### 1. 权限验证函数

#### `isWorkspaceAdminForMiddleware(workspaceId, userId)`
专为 middleware 优化的权限验证函数：
- 使用缓存机制减少数据库查询
- 集成详细的日志记录
- 返回简单的 boolean 值

#### `isWorkspaceAdmin(workspaceId, userId?)`
通用权限验证函数：
- 支持从 session 自动获取 userId
- 返回详细的验证结果
- 用于 server actions 和组件

### 2. 缓存系统

#### 特性
- **内存缓存**：5分钟过期时间
- **自动清理**：防止内存泄漏
- **智能失效**：权限变化时自动清理相关缓存

#### 使用方法
```typescript
import { getCachedAdminPermission, setCachedAdminPermission, clearUserAdminCache } from '@/app/utils/middleware-cache';

// 获取缓存
const cached = getCachedAdminPermission(userId, workspaceId);

// 设置缓存
setCachedAdminPermission(userId, workspaceId, true);

// 清理缓存
clearUserAdminCache(userId, workspaceId);
```

### 3. 日志系统

#### 记录内容
- 权限验证事件（GRANTED/DENIED/ERROR）
- 响应时间和缓存命中情况
- 用户代理和 IP 地址
- 错误原因和详细信息

#### 监控功能
- 性能指标统计
- 异常访问检测
- 缓存效率分析
- 日志导出功能

## 配置说明

### Middleware 配置

```typescript
// middleware.ts
export const config = {
  matcher: [
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    '/(api|trpc)(.*)',
  ],
};
```

### 环境变量

```env
# 开发环境下启用详细日志
NODE_ENV=development

# 生产环境配置
NODE_ENV=production
```

## 使用指南

### 1. 在 Server Actions 中使用权限验证

```typescript
import { isWorkspaceAdmin } from '@/app/utils/workspace';

export async function adminAction(workspaceId: string) {
  // 验证管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }
  
  // 执行管理员操作
  // ...
}
```

### 2. 在组件中使用权限检查

```typescript
import { useWorkspaceAdmin } from '@/app/hooks/useWorkspaceAdmin';

const AdminComponent = () => {
  const { isAdmin, isLoading } = useWorkspaceAdmin(workspaceId);
  
  if (isLoading) return <Loading />;
  if (!isAdmin) return <AccessDenied />;
  
  return <AdminContent />;
};
```

### 3. 权限变化时清理缓存

```typescript
import { clearUserPermissionCache } from '@/app/utils/workspace';

// 更新用户权限后
await updateUserRole(userId, workspaceId, newRole);

// 清理相关缓存
await clearUserPermissionCache(userId, workspaceId);
```

## 安全监控

### 访问监控面板

管理员可以通过 `/[workspace]/admin/security` 访问安全监控面板，查看：

- **实时性能指标**：请求数、缓存命中率、响应时间、错误率
- **访问日志**：详细的权限验证记录
- **异常检测**：可疑用户和异常访问模式
- **缓存统计**：缓存效率和使用情况

### API 接口

```typescript
// 获取安全数据
GET /api/[workspace]/admin/security?type=all&timeRange=3600000

// 管理操作
POST /api/[workspace]/admin/security
{
  "action": "clearCache" | "clearLogs" | "exportLogs" | "clearUserCache",
  "params": { ... }
}
```

## 性能优化

### 缓存策略
- **命中率目标**：> 70%
- **响应时间目标**：< 50ms
- **缓存大小限制**：1000 条目

### 监控指标
- 平均响应时间
- 缓存命中率
- 错误率
- 数据库查询次数

## 故障排除

### 常见问题

1. **权限验证失败**
   - 检查用户是否在 userWorkspace 表中
   - 确认 role 字段为 'owner' 或 'admin'
   - 验证 isActive 字段为 true

2. **缓存问题**
   - 权限变化后手动清理缓存
   - 检查缓存过期时间设置
   - 监控内存使用情况

3. **性能问题**
   - 检查数据库连接
   - 监控缓存命中率
   - 分析慢查询日志

### 调试模式

在开发环境中，系统会输出详细的调试信息：

```bash
[PERMISSION] GRANTED: User user-123 -> Workspace workspace-456 (cached, 12ms)
[MIDDLEWARE] Admin access granted: User user-123 accessing /workspace-456/admin/users
```

## 测试

### 单元测试

```bash
npm test app/utils/__tests__/middleware-permissions.test.ts
```

### 集成测试

测试覆盖：
- 权限验证逻辑
- 缓存机制
- 错误处理
- 日志记录

## 维护

### 定期任务
- 清理过期日志（每小时）
- 清理过期缓存（每10分钟）
- 性能指标收集（实时）

### 监控告警
- 错误率超过 1%
- 响应时间超过 200ms
- 缓存命中率低于 50%
- 检测到异常访问模式

## 更新日志

### v1.0.0 (2024-01-10)
- 实现基础的 middleware 权限控制
- 添加缓存机制和日志记录
- 创建安全监控面板
- 完善测试覆盖

## 贡献指南

1. 所有权限相关的修改都需要更新测试
2. 新增的权限检查点需要集成日志记录
3. 性能优化需要保持向后兼容性
4. 安全相关的变更需要额外的代码审查
