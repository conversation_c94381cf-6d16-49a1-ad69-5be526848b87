import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { isWorkspaceAdmin } from '@/app/utils/workspace';
import { 
  getPerformanceMetrics, 
  getRecentPermissionLogs, 
  getWorkspacePermissionLogs,
  detectAnomalousAccess,
  getCacheStats
} from '@/app/utils/middleware-logger';
import { getCacheStats as getPermissionCacheStats } from '@/app/utils/middleware-cache';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workspace: string }> }
) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workspace: workspaceId } = await params;

    // 验证管理员权限
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const timeRange = parseInt(searchParams.get('timeRange') || '3600000'); // 默认1小时
    const logLimit = parseInt(searchParams.get('logLimit') || '50');
    const dataType = searchParams.get('type') || 'all';

    let responseData: any = {};

    // 根据请求的数据类型返回相应数据
    if (dataType === 'all' || dataType === 'metrics') {
      responseData.metrics = getPerformanceMetrics(timeRange);
    }

    if (dataType === 'all' || dataType === 'logs') {
      responseData.recentLogs = getRecentPermissionLogs(logLimit);
      responseData.workspaceLogs = getWorkspacePermissionLogs(workspaceId, logLimit);
    }

    if (dataType === 'all' || dataType === 'anomalous') {
      responseData.anomalousAccess = detectAnomalousAccess(timeRange);
    }

    if (dataType === 'all' || dataType === 'cache') {
      responseData.cacheStats = {
        logger: getCacheStats(),
        permission: getPermissionCacheStats()
      };
    }

    return NextResponse.json({
      success: true,
      data: responseData,
      timestamp: Date.now(),
      timeRange,
      workspaceId
    });

  } catch (error) {
    console.error('Security monitoring API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workspace: string }> }
) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workspace: workspaceId } = await params;

    // 验证管理员权限
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, ...params } = body;

    switch (action) {
      case 'clearCache':
        // 清理权限缓存
        const { clearAllAdminCache } = await import('@/app/utils/middleware-cache');
        clearAllAdminCache();
        
        return NextResponse.json({
          success: true,
          message: 'Permission cache cleared successfully'
        });

      case 'clearLogs':
        // 清理日志（这里需要实现具体的清理逻辑）
        const { cleanupOldLogs } = await import('@/app/utils/middleware-logger');
        const olderThan = params.olderThan || 24 * 60 * 60 * 1000; // 默认24小时
        cleanupOldLogs(olderThan);
        
        return NextResponse.json({
          success: true,
          message: 'Logs cleaned up successfully'
        });

      case 'exportLogs':
        // 导出日志
        const { exportLogs } = await import('@/app/utils/middleware-logger');
        const format = params.format || 'json';
        const exportData = exportLogs(format as 'json' | 'csv');
        
        return NextResponse.json({
          success: true,
          data: exportData,
          format
        });

      case 'clearUserCache':
        // 清理特定用户的缓存
        const { clearUserAdminCache } = await import('@/app/utils/middleware-cache');
        const { userId, targetWorkspaceId } = params;
        
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required' },
            { status: 400 }
          );
        }
        
        clearUserAdminCache(userId, targetWorkspaceId);
        
        return NextResponse.json({
          success: true,
          message: `Cache cleared for user ${userId}`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Security monitoring action error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 支持的HTTP方法
export const dynamic = 'force-dynamic';
