/**
 * Middleware 权限控制日志记录和监控工具
 * 用于记录权限验证事件和性能监控
 */

interface PermissionLogEntry {
  timestamp: number;
  userId: string;
  workspaceId: string;
  action: 'GRANTED' | 'DENIED' | 'ERROR';
  reason?: string;
  fromCache: boolean;
  duration: number;
  userAgent?: string;
  ip?: string;
}

interface PerformanceMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  errorCount: number;
  deniedCount: number;
  grantedCount: number;
}

// 内存中的日志存储（生产环境中应该使用外部日志系统）
const permissionLogs: PermissionLogEntry[] = [];
const MAX_LOG_ENTRIES = 1000; // 最大日志条目数

/**
 * 记录权限验证事件
 * @param entry - 权限日志条目
 */
export function logPermissionEvent(entry: Omit<PermissionLogEntry, 'timestamp'>): void {
  const logEntry: PermissionLogEntry = {
    ...entry,
    timestamp: Date.now()
  };

  permissionLogs.push(logEntry);

  // 保持日志数量在限制内
  if (permissionLogs.length > MAX_LOG_ENTRIES) {
    permissionLogs.splice(0, permissionLogs.length - MAX_LOG_ENTRIES);
  }

  // 在开发环境中输出到控制台
  if (process.env.NODE_ENV === 'development') {
    console.log(`[PERMISSION] ${entry.action}: User ${entry.userId} -> Workspace ${entry.workspaceId} (${entry.fromCache ? 'cached' : 'db'}, ${entry.duration}ms)`);
  }

  // 如果是错误或拒绝访问，记录更详细的信息
  if (entry.action === 'ERROR' || entry.action === 'DENIED') {
    console.warn(`[PERMISSION WARNING] ${entry.action}: User ${entry.userId} attempted to access workspace ${entry.workspaceId}`, {
      reason: entry.reason,
      userAgent: entry.userAgent,
      ip: entry.ip
    });
  }
}

/**
 * 获取性能指标
 * @param timeRangeMs - 时间范围（毫秒），默认为最近1小时
 * @returns 性能指标
 */
export function getPerformanceMetrics(timeRangeMs: number = 60 * 60 * 1000): PerformanceMetrics {
  const cutoffTime = Date.now() - timeRangeMs;
  const recentLogs = permissionLogs.filter(log => log.timestamp >= cutoffTime);

  if (recentLogs.length === 0) {
    return {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      errorCount: 0,
      deniedCount: 0,
      grantedCount: 0
    };
  }

  const cacheHits = recentLogs.filter(log => log.fromCache).length;
  const cacheMisses = recentLogs.length - cacheHits;
  const totalDuration = recentLogs.reduce((sum, log) => sum + log.duration, 0);
  const averageResponseTime = totalDuration / recentLogs.length;

  const errorCount = recentLogs.filter(log => log.action === 'ERROR').length;
  const deniedCount = recentLogs.filter(log => log.action === 'DENIED').length;
  const grantedCount = recentLogs.filter(log => log.action === 'GRANTED').length;

  return {
    totalRequests: recentLogs.length,
    cacheHits,
    cacheMisses,
    averageResponseTime: Math.round(averageResponseTime * 100) / 100,
    errorCount,
    deniedCount,
    grantedCount
  };
}

/**
 * 获取最近的权限日志
 * @param limit - 返回的日志条目数量限制
 * @returns 最近的权限日志
 */
export function getRecentPermissionLogs(limit: number = 50): PermissionLogEntry[] {
  return permissionLogs
    .slice(-limit)
    .reverse(); // 最新的在前面
}

/**
 * 获取特定用户的权限日志
 * @param userId - 用户ID
 * @param limit - 返回的日志条目数量限制
 * @returns 用户的权限日志
 */
export function getUserPermissionLogs(userId: string, limit: number = 20): PermissionLogEntry[] {
  return permissionLogs
    .filter(log => log.userId === userId)
    .slice(-limit)
    .reverse();
}

/**
 * 获取特定工作空间的权限日志
 * @param workspaceId - 工作空间ID
 * @param limit - 返回的日志条目数量限制
 * @returns 工作空间的权限日志
 */
export function getWorkspacePermissionLogs(workspaceId: string, limit: number = 20): PermissionLogEntry[] {
  return permissionLogs
    .filter(log => log.workspaceId === workspaceId)
    .slice(-limit)
    .reverse();
}

/**
 * 清理旧的日志条目
 * @param olderThanMs - 清理多久之前的日志（毫秒）
 */
export function cleanupOldLogs(olderThanMs: number = 24 * 60 * 60 * 1000): void {
  const cutoffTime = Date.now() - olderThanMs;
  const initialLength = permissionLogs.length;
  
  // 移除旧的日志条目
  for (let i = permissionLogs.length - 1; i >= 0; i--) {
    if (permissionLogs[i].timestamp < cutoffTime) {
      permissionLogs.splice(i, 1);
    }
  }

  const removedCount = initialLength - permissionLogs.length;
  if (removedCount > 0) {
    console.log(`[PERMISSION CLEANUP] Removed ${removedCount} old log entries`);
  }
}

/**
 * 检测异常访问模式
 * @param timeRangeMs - 检测时间范围（毫秒）
 * @returns 异常访问报告
 */
export function detectAnomalousAccess(timeRangeMs: number = 10 * 60 * 1000) {
  const cutoffTime = Date.now() - timeRangeMs;
  const recentLogs = permissionLogs.filter(log => log.timestamp >= cutoffTime);

  // 统计每个用户的拒绝访问次数
  const deniedAccessByUser = new Map<string, number>();
  const errorsByUser = new Map<string, number>();

  recentLogs.forEach(log => {
    if (log.action === 'DENIED') {
      deniedAccessByUser.set(log.userId, (deniedAccessByUser.get(log.userId) || 0) + 1);
    }
    if (log.action === 'ERROR') {
      errorsByUser.set(log.userId, (errorsByUser.get(log.userId) || 0) + 1);
    }
  });

  // 检测异常模式
  const suspiciousUsers: string[] = [];
  const errorProneUsers: string[] = [];

  // 如果用户在短时间内被拒绝访问超过5次，标记为可疑
  deniedAccessByUser.forEach((count, userId) => {
    if (count >= 5) {
      suspiciousUsers.push(userId);
    }
  });

  // 如果用户在短时间内出现超过3次错误，标记为错误频发
  errorsByUser.forEach((count, userId) => {
    if (count >= 3) {
      errorProneUsers.push(userId);
    }
  });

  return {
    timeRange: timeRangeMs,
    totalEvents: recentLogs.length,
    suspiciousUsers,
    errorProneUsers,
    deniedAccessCount: Array.from(deniedAccessByUser.values()).reduce((sum, count) => sum + count, 0),
    errorCount: Array.from(errorsByUser.values()).reduce((sum, count) => sum + count, 0)
  };
}

/**
 * 导出日志数据（用于外部分析）
 * @param format - 导出格式
 * @returns 格式化的日志数据
 */
export function exportLogs(format: 'json' | 'csv' = 'json'): string {
  if (format === 'csv') {
    const headers = ['timestamp', 'userId', 'workspaceId', 'action', 'reason', 'fromCache', 'duration', 'userAgent', 'ip'];
    const csvRows = [
      headers.join(','),
      ...permissionLogs.map(log => [
        new Date(log.timestamp).toISOString(),
        log.userId,
        log.workspaceId,
        log.action,
        log.reason || '',
        log.fromCache.toString(),
        log.duration.toString(),
        log.userAgent || '',
        log.ip || ''
      ].map(field => `"${field}"`).join(','))
    ];
    return csvRows.join('\n');
  }

  return JSON.stringify(permissionLogs, null, 2);
}

// 定期清理旧日志（每小时执行一次）
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    cleanupOldLogs();
  }, 60 * 60 * 1000);
}
