/**
 * Middleware 权限控制测试
 * 用于验证权限验证和缓存机制的正确性
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { isWorkspaceAdminForMiddleware, clearUserPermissionCache } from '../workspace';
import { 
  getCachedAdminPermission, 
  setCachedAdminPermission, 
  clearAllAdminCache,
  getCacheStats 
} from '../middleware-cache';

// Mock 数据库查询
jest.mock('@/app/db', () => ({
  db: {
    query: {
      userWorkspace: {
        findFirst: jest.fn()
      }
    }
  }
}));

// Mock auth
jest.mock('@/auth', () => ({
  auth: jest.fn()
}));

describe('Middleware 权限控制', () => {
  const mockUserId = 'user-123';
  const mockWorkspaceId = 'workspace-456';

  beforeEach(() => {
    // 清理所有缓存
    clearAllAdminCache();
    jest.clearAllMocks();
  });

  afterEach(() => {
    clearAllAdminCache();
  });

  describe('权限缓存机制', () => {
    it('应该正确设置和获取缓存', () => {
      // 初始状态应该没有缓存
      expect(getCachedAdminPermission(mockUserId, mockWorkspaceId)).toBeNull();

      // 设置缓存
      setCachedAdminPermission(mockUserId, mockWorkspaceId, true);

      // 应该能够获取缓存的值
      expect(getCachedAdminPermission(mockUserId, mockWorkspaceId)).toBe(true);
    });

    it('应该正确处理缓存过期', async () => {
      // 设置缓存
      setCachedAdminPermission(mockUserId, mockWorkspaceId, true);
      
      // 模拟时间过期（通过修改时间戳）
      const cacheStats = getCacheStats();
      expect(cacheStats.validCount).toBe(1);

      // 等待一小段时间后检查（实际测试中可能需要 mock Date.now()）
      // 这里只是验证缓存机制的基本功能
    });

    it('应该正确清理用户缓存', async () => {
      // 设置多个缓存
      setCachedAdminPermission(mockUserId, mockWorkspaceId, true);
      setCachedAdminPermission(mockUserId, 'workspace-789', false);
      setCachedAdminPermission('user-456', mockWorkspaceId, true);

      // 清理特定用户的特定工作空间缓存
      await clearUserPermissionCache(mockUserId, mockWorkspaceId);

      // 验证只有指定的缓存被清理
      expect(getCachedAdminPermission(mockUserId, mockWorkspaceId)).toBeNull();
      expect(getCachedAdminPermission(mockUserId, 'workspace-789')).toBe(false);
      expect(getCachedAdminPermission('user-456', mockWorkspaceId)).toBe(true);
    });
  });

  describe('权限验证函数', () => {
    it('应该正确验证管理员权限', async () => {
      const { db } = await import('@/app/db');
      
      // Mock 数据库返回 owner 角色
      (db.query.userWorkspace.findFirst as jest.Mock).mockResolvedValue({
        role: 'owner'
      });

      const result = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
      expect(result).toBe(true);

      // 验证缓存被设置
      expect(getCachedAdminPermission(mockUserId, mockWorkspaceId)).toBe(true);
    });

    it('应该正确验证非管理员权限', async () => {
      const { db } = await import('@/app/db');
      
      // Mock 数据库返回 member 角色
      (db.query.userWorkspace.findFirst as jest.Mock).mockResolvedValue({
        role: 'member'
      });

      const result = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
      expect(result).toBe(false);

      // 验证缓存被设置
      expect(getCachedAdminPermission(mockUserId, mockWorkspaceId)).toBe(false);
    });

    it('应该正确处理用户不存在的情况', async () => {
      const { db } = await import('@/app/db');
      
      // Mock 数据库返回 null
      (db.query.userWorkspace.findFirst as jest.Mock).mockResolvedValue(null);

      const result = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
      expect(result).toBe(false);

      // 验证缓存被设置
      expect(getCachedAdminPermission(mockUserId, mockWorkspaceId)).toBe(false);
    });

    it('应该正确处理数据库错误', async () => {
      const { db } = await import('@/app/db');
      
      // Mock 数据库抛出错误
      (db.query.userWorkspace.findFirst as jest.Mock).mockRejectedValue(new Error('Database error'));

      const result = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
      expect(result).toBe(false);
    });

    it('应该使用缓存避免重复数据库查询', async () => {
      const { db } = await import('@/app/db');
      
      // 第一次调用
      (db.query.userWorkspace.findFirst as jest.Mock).mockResolvedValue({
        role: 'admin'
      });

      const result1 = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
      expect(result1).toBe(true);
      expect(db.query.userWorkspace.findFirst).toHaveBeenCalledTimes(1);

      // 第二次调用应该使用缓存
      const result2 = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
      expect(result2).toBe(true);
      expect(db.query.userWorkspace.findFirst).toHaveBeenCalledTimes(1); // 没有增加
    });
  });

  describe('缓存统计', () => {
    it('应该正确报告缓存统计信息', () => {
      // 添加一些缓存条目
      setCachedAdminPermission('user1', 'workspace1', true);
      setCachedAdminPermission('user2', 'workspace2', false);
      setCachedAdminPermission('user3', 'workspace3', true);

      const stats = getCacheStats();
      expect(stats.totalSize).toBe(3);
      expect(stats.validCount).toBe(3);
      expect(stats.expiredCount).toBe(0);
    });
  });
});

// 集成测试示例
describe('Middleware 集成测试', () => {
  it('应该模拟完整的权限验证流程', async () => {
    const mockUserId = 'integration-user';
    const mockWorkspaceId = 'integration-workspace';

    // 模拟用户是管理员
    const { db } = await import('@/app/db');
    (db.query.userWorkspace.findFirst as jest.Mock).mockResolvedValue({
      role: 'owner'
    });

    // 第一次验证（应该查询数据库）
    const result1 = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
    expect(result1).toBe(true);

    // 第二次验证（应该使用缓存）
    const result2 = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
    expect(result2).toBe(true);

    // 模拟权限变化，清理缓存
    await clearUserPermissionCache(mockUserId, mockWorkspaceId);

    // 模拟权限被撤销
    (db.query.userWorkspace.findFirst as jest.Mock).mockResolvedValue({
      role: 'member'
    });

    // 第三次验证（应该重新查询数据库）
    const result3 = await isWorkspaceAdminForMiddleware(mockWorkspaceId, mockUserId);
    expect(result3).toBe(false);
  });
});
