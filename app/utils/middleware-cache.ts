/**
 * Middleware 权限验证缓存机制
 * 用于提高权限验证的性能，减少数据库查询
 */

interface AdminPermissionCache {
  isAdmin: boolean;
  timestamp: number;
}

// 内存缓存，存储用户权限信息
const adminPermissionCache = new Map<string, AdminPermissionCache>();

// 缓存过期时间：5分钟
const CACHE_DURATION = 5 * 60 * 1000;

// 最大缓存条目数，防止内存泄漏
const MAX_CACHE_SIZE = 1000;

/**
 * 生成缓存键
 * @param userId - 用户ID
 * @param workspaceId - 工作空间ID
 * @returns 缓存键
 */
function getCacheKey(userId: string, workspaceId: string): string {
  return `${userId}:${workspaceId}`;
}

/**
 * 获取缓存的权限信息
 * @param userId - 用户ID
 * @param workspaceId - 工作空间ID
 * @returns 缓存的权限信息，如果不存在或已过期则返回 null
 */
export function getCachedAdminPermission(userId: string, workspaceId: string): boolean | null {
  const cacheKey = getCacheKey(userId, workspaceId);
  const cached = adminPermissionCache.get(cacheKey);

  if (!cached) {
    return null;
  }

  // 检查是否过期
  if (Date.now() - cached.timestamp > CACHE_DURATION) {
    adminPermissionCache.delete(cacheKey);
    return null;
  }

  return cached.isAdmin;
}

/**
 * 设置权限缓存
 * @param userId - 用户ID
 * @param workspaceId - 工作空间ID
 * @param isAdmin - 是否为管理员
 */
export function setCachedAdminPermission(userId: string, workspaceId: string, isAdmin: boolean): void {
  // 如果缓存已满，清理过期的条目
  if (adminPermissionCache.size >= MAX_CACHE_SIZE) {
    cleanExpiredCache();
  }

  // 如果清理后仍然满了，删除最旧的条目
  if (adminPermissionCache.size >= MAX_CACHE_SIZE) {
    const firstKey = adminPermissionCache.keys().next().value;
    if (firstKey) {
      adminPermissionCache.delete(firstKey);
    }
  }

  const cacheKey = getCacheKey(userId, workspaceId);
  adminPermissionCache.set(cacheKey, {
    isAdmin,
    timestamp: Date.now()
  });
}

/**
 * 清理过期的缓存条目
 */
function cleanExpiredCache(): void {
  const now = Date.now();
  const expiredKeys: string[] = [];

  for (const [key, value] of adminPermissionCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      expiredKeys.push(key);
    }
  }

  expiredKeys.forEach(key => adminPermissionCache.delete(key));
}

/**
 * 清除特定用户的权限缓存
 * 当用户权限发生变化时调用
 * @param userId - 用户ID
 * @param workspaceId - 工作空间ID（可选，如果不提供则清除该用户的所有缓存）
 */
export function clearUserAdminCache(userId: string, workspaceId?: string): void {
  if (workspaceId) {
    // 清除特定工作空间的缓存
    const cacheKey = getCacheKey(userId, workspaceId);
    adminPermissionCache.delete(cacheKey);
  } else {
    // 清除该用户的所有缓存
    const keysToDelete: string[] = [];
    for (const key of adminPermissionCache.keys()) {
      if (key.startsWith(`${userId}:`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => adminPermissionCache.delete(key));
  }
}

/**
 * 清除所有缓存
 * 用于系统维护或重置
 */
export function clearAllAdminCache(): void {
  adminPermissionCache.clear();
}

/**
 * 获取缓存统计信息
 * 用于监控和调试
 */
export function getCacheStats() {
  const now = Date.now();
  let validCount = 0;
  let expiredCount = 0;

  for (const value of adminPermissionCache.values()) {
    if (now - value.timestamp > CACHE_DURATION) {
      expiredCount++;
    } else {
      validCount++;
    }
  }

  return {
    totalSize: adminPermissionCache.size,
    validCount,
    expiredCount,
    maxSize: MAX_CACHE_SIZE,
    cacheDuration: CACHE_DURATION
  };
}

// 定期清理过期缓存（每10分钟）
if (typeof setInterval !== 'undefined') {
  setInterval(cleanExpiredCache, 10 * 60 * 1000);
}
