'use client';

import React, { useState, useEffect } from 'react';
import { Card, Statistic, Table, Tag, Button, Space, Alert, Tabs, Row, Col } from 'antd';
import { 
  WarningOutlined, 
  ClockCircleOutlined,
  UserOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

interface PermissionLog {
  timestamp: number;
  userId: string;
  workspaceId: string;
  action: 'GRANTED' | 'DENIED' | 'ERROR';
  reason?: string;
  fromCache: boolean;
  duration: number;
  userAgent?: string;
  ip?: string;
}

interface PerformanceMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  errorCount: number;
  deniedCount: number;
  grantedCount: number;
}

const SecurityMonitorPage = () => {
  const t = useTranslations('Admin.Security');
  const params = useParams();
  const workspaceId = params.workspace as string;

  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [recentLogs, setRecentLogs] = useState<PermissionLog[]>([]);
  const [workspaceLogs, setWorkspaceLogs] = useState<PermissionLog[]>([]);
  const [anomalousAccess, setAnomalousAccess] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // 模拟数据加载（实际应该从API获取）
  const loadSecurityData = async () => {
    setLoading(true);
    try {
      // 这里应该调用实际的API来获取安全数据
      // 现在使用模拟数据
      const mockMetrics: PerformanceMetrics = {
        totalRequests: 1250,
        cacheHits: 980,
        cacheMisses: 270,
        averageResponseTime: 45.2,
        errorCount: 12,
        deniedCount: 8,
        grantedCount: 1230
      };

      const mockLogs: PermissionLog[] = [
        {
          timestamp: Date.now() - 1000 * 60 * 5,
          userId: 'user-123',
          workspaceId: workspaceId,
          action: 'GRANTED',
          fromCache: true,
          duration: 12
        },
        {
          timestamp: Date.now() - 1000 * 60 * 10,
          userId: 'user-456',
          workspaceId: workspaceId,
          action: 'DENIED',
          reason: 'Insufficient permissions',
          fromCache: false,
          duration: 89
        },
        {
          timestamp: Date.now() - 1000 * 60 * 15,
          userId: 'user-789',
          workspaceId: workspaceId,
          action: 'ERROR',
          reason: 'Database connection failed',
          fromCache: false,
          duration: 5000
        }
      ];

      const mockAnomalous = {
        timeRange: 600000,
        totalEvents: 45,
        suspiciousUsers: ['user-suspicious'],
        errorProneUsers: ['user-error-prone'],
        deniedAccessCount: 8,
        errorCount: 3
      };

      setMetrics(mockMetrics);
      setRecentLogs(mockLogs);
      setWorkspaceLogs(mockLogs.filter(log => log.workspaceId === workspaceId));
      setAnomalousAccess(mockAnomalous);
    } catch (error) {
      console.error('Failed to load security data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSecurityData();
    // 每30秒刷新一次数据
    const interval = setInterval(loadSecurityData, 30000);
    return () => clearInterval(interval);
  }, [workspaceId]);

  const getActionColor = (action: string) => {
    switch (action) {
      case 'GRANTED': return 'green';
      case 'DENIED': return 'orange';
      case 'ERROR': return 'red';
      default: return 'default';
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const logColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => formatTimestamp(timestamp),
      width: 180,
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      key: 'userId',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (action: string) => (
        <Tag color={getActionColor(action)}>{action}</Tag>
      ),
      width: 100,
    },
    {
      title: '原因',
      dataIndex: 'reason',
      key: 'reason',
      render: (reason?: string) => reason || '-',
    },
    {
      title: '缓存',
      dataIndex: 'fromCache',
      key: 'fromCache',
      render: (fromCache: boolean) => (
        <Tag color={fromCache ? 'blue' : 'default'}>
          {fromCache ? '缓存' : '数据库'}
        </Tag>
      ),
      width: 80,
    },
    {
      title: '耗时(ms)',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => (
        <span style={{ color: duration > 1000 ? '#ff4d4f' : duration > 100 ? '#faad14' : '#52c41a' }}>
          {duration}
        </span>
      ),
      width: 100,
    },
  ];

  const cacheHitRate = metrics ? ((metrics.cacheHits / metrics.totalRequests) * 100).toFixed(1) : '0';
  const errorRate = metrics ? ((metrics.errorCount / metrics.totalRequests) * 100).toFixed(2) : '0';

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">安全监控</h1>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={loadSecurityData}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>

      {/* 异常访问警告 */}
      {anomalousAccess && (anomalousAccess.suspiciousUsers.length > 0 || anomalousAccess.errorProneUsers.length > 0) && (
        <Alert
          message="检测到异常访问模式"
          description={
            <div>
              {anomalousAccess.suspiciousUsers.length > 0 && (
                <div>可疑用户: {anomalousAccess.suspiciousUsers.join(', ')}</div>
              )}
              {anomalousAccess.errorProneUsers.length > 0 && (
                <div>频繁错误用户: {anomalousAccess.errorProneUsers.join(', ')}</div>
              )}
            </div>
          }
          type="warning"
          showIcon
          className="mb-6"
        />
      )}

      {/* 性能指标卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总请求数"
              value={metrics?.totalRequests || 0}
              prefix={<EyeOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="缓存命中率"
              value={cacheHitRate}
              suffix="%"
              valueStyle={{ color: parseFloat(cacheHitRate) > 70 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均响应时间"
              value={metrics?.averageResponseTime || 0}
              suffix="ms"
              prefix={<ClockCircleOutlined />}
              precision={1}
              valueStyle={{ 
                color: (metrics?.averageResponseTime || 0) < 50 ? '#3f8600' : 
                       (metrics?.averageResponseTime || 0) < 200 ? '#faad14' : '#cf1322' 
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="错误率"
              value={errorRate}
              suffix="%"
              prefix={<WarningOutlined />}
              precision={2}
              valueStyle={{ color: parseFloat(errorRate) < 1 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="访问统计" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="允许访问"
                  value={metrics?.grantedCount || 0}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="拒绝访问"
                  value={metrics?.deniedCount || 0}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="错误次数"
                  value={metrics?.errorCount || 0}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="缓存统计" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="缓存命中"
                  value={metrics?.cacheHits || 0}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="缓存未命中"
                  value={metrics?.cacheMisses || 0}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 日志表格 */}
      <Tabs
        items={[
          {
            key: 'workspace',
            label: '当前工作空间日志',
            children: (
              <Table
                columns={logColumns}
                dataSource={workspaceLogs}
                rowKey={(record) => `${record.timestamp}-${record.userId}`}
                pagination={{ pageSize: 10 }}
                size="small"
                loading={loading}
              />
            ),
          },
          {
            key: 'recent',
            label: '最近日志',
            children: (
              <Table
                columns={logColumns}
                dataSource={recentLogs}
                rowKey={(record) => `${record.timestamp}-${record.userId}`}
                pagination={{ pageSize: 10 }}
                size="small"
                loading={loading}
              />
            ),
          },
        ]}
      />
    </div>
  );
};

export default SecurityMonitorPage;
