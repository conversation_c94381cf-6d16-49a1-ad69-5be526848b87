import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { auth } from "@/auth"
import { isWorkspaceAdminForMiddleware } from '@/app/utils/workspace'

export async function middleware(request: NextRequest) {
  // 首先进行基础认证检查
  const session = await auth()

  // 检查是否为admin路由
  const adminRouteMatch = request.nextUrl.pathname.match(/^\/([^\/]+)\/admin(?:\/.*)?$/)

  if (adminRouteMatch) {
    const workspaceId = adminRouteMatch[1]

    // 如果用户未登录，重定向到登录页面
    if (!session?.user?.id) {
      const loginUrl = new URL('/login', request.url)
      // 添加回调URL，登录后重定向回原页面
      loginUrl.searchParams.set('callbackUrl', request.url)
      return NextResponse.redirect(loginUrl)
    }

    try {
      // 验证用户是否为该workspace的管理员
      const isAdmin = await isWorkspaceAdminForMiddleware(workspaceId, session.user.id)

      if (!isAdmin) {
        // 记录访问被拒绝的详细信息
        console.warn(`[MIDDLEWARE] Admin access denied: User ${session.user.id} attempted to access ${request.nextUrl.pathname}`, {
          userAgent: request.headers.get('user-agent'),
          ip: request.ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
          referer: request.headers.get('referer')
        });

        // 返回404而非403，隐藏路由存在性
        return new NextResponse(null, {
          status: 404,
          statusText: 'Not Found'
        })
      }

      // 记录成功的管理员访问
      if (process.env.NODE_ENV === 'development') {
        console.log(`[MIDDLEWARE] Admin access granted: User ${session.user.id} accessing ${request.nextUrl.pathname}`);
      }
    } catch (error) {
      console.error('Admin route permission check failed:', error)

      // 记录权限检查错误
      console.error(`[MIDDLEWARE] Permission check error for user ${session.user.id} accessing ${request.nextUrl.pathname}:`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        userAgent: request.headers.get('user-agent'),
        ip: request.ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
      });

      // 发生错误时也返回404，避免暴露系统信息
      return new NextResponse(null, {
        status: 404,
        statusText: 'Not Found'
      })
    }
  }

  // 对于非admin路由，只进行基础认证检查
  if (!session && request.nextUrl.pathname.startsWith('/api/')) {
    return new NextResponse(
      JSON.stringify({ error: 'Unauthorized' }),
      {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};